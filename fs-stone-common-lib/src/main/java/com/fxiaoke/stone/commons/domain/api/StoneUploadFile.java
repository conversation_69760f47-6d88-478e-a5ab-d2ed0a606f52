package com.fxiaoke.stone.commons.domain.api;

import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface StoneUploadFile {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    /**
     * 企业账号
     * 非空
     */
    private String ea;

    /**
     * 员工ID,系统调用传 -10000
     * 非空
     */
    private Integer employeeId;

    /**
     *  resourceType 文件资源类型
     *  非空,如 N、TN、C、TC
     *  注: C|TC 类型为无隐私CDN加速文件,要求文件类型必须为图片,且仅支持JPG、JPEG、PNG、GIF、BMP、WEBP类型;
     *  注: 如果文件实际类型为其他类型,请使用N|TN类型,否则即使指定为C|TC类型,上传时也会被转换为N|TN类型;
     */
    private FileResourceEnum resourceType;
    /**
     * 业务线标识
     * 非空
     */
    private String business;

    /**
     * 安全组
     * 传 `XiaokeNetDisk` 则上传为网盘文件
     * 如上传成网盘文件则下载受限不能直接通过Path下载文件
     */
    private String securityGroup;

    /**
     * 文件扩展名
     * 非空
     */
    private String extension;

    /**
     * 是否上传为临时文件
     * 非空
     * 如果为true则上传为临时文件(不设置有效期默认3天删除，设置有效期后按设置的有效期删除)
     * 如果为false则上传为正式文件(不设置有效期默认永久保存，设置有效期后按设置的有效期删除)
     */
    private Boolean tempFile;

    /**
     * 有效期（单位：天）
     * 默认 3 天
     * 结合 tempFile 使用
     */
    private Integer expireDay;

    /**
     * 文件大小
     * 不传则先将文件缓存到本地磁盘,获取到文件大小后再上传，如果传入则直接上传
     * 如果传入的 fileSize 与实际大小不一致，则会抛出异常
     */
    private Integer fileSize;

    /**
     * 文件Hash值
     * 一般为 MD5 值,用于校验文件完整性与防止重复上传
     */
    private String hashCode;

    /**
     * 图片文件是否保持原格式
     * 默认为 false , 转换为 webp格式保存
     */
    private Boolean keepFormat;
  }

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {

    /**
     * 文件path 如 N_202508_13_16c00c926c20414a954bb5e04049c542
     */
    private String path;
    /**
     * 文件大小
     */
    private Long size;
    /**
     * 文件扩展名
     */
    private String extension;
  }
}
