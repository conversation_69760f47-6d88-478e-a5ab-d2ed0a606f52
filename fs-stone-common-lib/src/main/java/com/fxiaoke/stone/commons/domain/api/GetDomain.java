package com.fxiaoke.stone.commons.domain.api;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface GetDomain {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    /**
     * 是谁要访问该文件
     */
    private long tenantId;
    /**
     * 域名灰度使用
     */
    private long userId;

    private String business;
  }

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {
    private List<String> homeDomains;
    private List<String> fileDomains;
    /**
     * CDN 文件
     */
    private List<String> cdnDomains;
  }

}
