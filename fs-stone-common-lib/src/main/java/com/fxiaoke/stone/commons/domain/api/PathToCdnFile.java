package com.fxiaoke.stone.commons.domain.api;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

public interface PathToCdnFile {

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Arg {

    /**
     * 文件所属企业
     */
    private long tenantId;
    private long userId;

    private String business;
    private String businessUnit;

    /**
     * 支持N|TN|C|TC
     */
    private String path;

    // TODO: 可能没有
    private long fileSize;
    // TODO: 存在部分数据没有
    private String filename;

    /**
     * 当前仅支持 图片（jpg、webp、png、jpeg、bmp)
     */
    private String extension;

    /**
     * 后期做幂等用的
     */
    private String fileHash;

    /**
     * 打标记（标识业务、标识一类数据、最多10个）
     */
    private List<String> tags;

    /**
     * 描述（描述图片的作用及内容，也可用于备注）
     */
    private String description;
  }

  @Data
  @ToString
  @EqualsAndHashCode
  @NoArgsConstructor
  @AllArgsConstructor
  class Result {
    // cdn文件无域名路径
    // /imgae.png
    private String cdnFileNoDomainPath;
  }
}
