package com.fxiaoke.stone.commons;

import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.io.InputStream;

/**
 * Stone 文件服务客户端接口
 *
 * <p>提供文件上传功能，支持流式上传和缓存上传两种模式。</p>
 */
public interface StoneFileClient {

  /**
   * 上传N类型文件
   *
   * @param arg 上传参数
   * @param maxCacheSize 最大缓存大小（字节），当文件大小未知时使用
   * @param bufferSize 缓冲区大小（字节）
   * @param stream 文件输入流
   * @return 上传结果
   * @throws StoneCommonClientException 上传失败时抛出
   */
  StoneUploadFile.Result uploadNFile(StoneUploadFile.Arg arg, long maxCacheSize, int bufferSize,
      InputStream stream) throws StoneCommonClientException;
}
