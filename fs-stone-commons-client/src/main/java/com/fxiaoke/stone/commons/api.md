好的，遵照您的指示，我将为您生成一份详细的Markdown格式的API说明文档。

# API 文档

## 文件上传

此端点用于将文件上传到服务器。

### 请求

---

`POST http://fs-stone-proxy/api/v1/n/file`

#### **Head 参数**

| 参数 | 类型 | 说明 |
| :--- | :--- | :--- |
| `x-fs-rest-user-ext` | `String` | 一个包含文件元数据和处理指令的 JSON 字符串。 |

**`x-fs-rest-user-ext` JSON 对象属性说明：**

| 属性 | 类型 | 必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `ea` | `String` | 否 | 企业账号。 |
| `employee_id` | `Integer` | 否 | 员工ID。 |
| `business` | `String` | 是 | 业务线标识。 |
| `security_group` | `String` | 否 | 文件安全组。 |
| `permissions` | `Array` | 否 | 权限组 (基本废弃可忽略)。 |
| `global` | `Boolean` | 否 | 是否全局可访问 (基本废弃可忽略)。 |
| `extension_name` | `String` | 是 | 文件扩展名。 |
| `named_path` | `String` | 否 | 设置文件唯一ID。如果指定，系统将使用此ID，否则将自动生成。 |
| `expire_day` | `Integer` | 否 | 设置文件过期时间（天）。0 表示永不过期。 |
| `image_process_request` | `Object` | 否 | 图片处理参数。 |
| `├─ make_thumbnail_list` | `Array` | 否 | 需要生成的缩略图尺寸列表，格式为 "宽x高" 的字符串数组，例如 `["100x100", "200x200"]`。 |
| `├─ original_image_optional_zoom_out` | `Integer` | 否 | 原图缩放选项。 |
| `└─ words` | `Array` | 否 | 添加到缩略图的水印文字数组。 |
| `code` | `String` | 否 | 文件的 MD5 哈希值，用于校验文件完整性。 |
| `need_thumbnail` | `Boolean` | 否 | 是否需要异步生成缩略图。 |
| `keep_format` | `Boolean` | 否 | 是否保持图片原始格式。 |
| `need_cdn` | `Boolean` | 否 | 是否将文件上传为 CDN 文件。 |
| `file_size` | `Integer` | 是 | 文件大小（以字节为单位）。 |
| `origin_name` | `String` | 否 | 文件原始名称。 |
| `words` | `Array` | 否 | 添加到原始图片的水印文字数组。 |
| `api_name` | `String` | 否 | API 名称 (基本废弃可忽略)。 |

#### **Body 参数**

| 参数 | 类型 | 说明 |
| :--- | :--- | :--- |
| `InputStream` | `Binary` | 文件的二进制数据流。 |

---

### **响应**

#### **成功响应**

**状态码:** `200 OK`

**响应体:**

```json
{
    "path": "group1/M00/00/01/CgAA_l7v1a-aAbcdAABcdeFg_123.jpg",
    "size": "123456",
    "extensionName": "jpg"
}
```

**响应参数说明：**

| 属性 | 类型 | 描述 |
| :--- | :--- | :--- |
| `path` | `String` | 文件在服务器上的唯一ID或路径。 |
| `size` | `String` | 文件的大小（以字节为单位）。 |
| `extensionName` | `String` | 文件的扩展名。 |

#### **失败响应**
```java
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public final class FRestClientException extends Exception {
  /*
    国际错误标准码
     */
  private String code;
  /*
  错误信息
   */
  private String message;
}
```

**示例 cURL 请求:**

```bash
curl --location --request POST 'http://fs-stone-proxy/api/v1/n/file' \
--header 'x-fs-rest-user-ext: {"ea": "your_ea","employee_id": 123,"business": "your_business_line","extension_name": "jpg","file_size": 123456,"origin_name": "example.jpg"}' \
--data-binary '@/path/to/your/local/file.jpg'
```