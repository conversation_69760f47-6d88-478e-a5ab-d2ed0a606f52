package com.fxiaoke.stone.commons.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.domain.R;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CallServiceUtil {

  public static String post(OkHttpSupport client,String serverUrl, RequestBody body) {
    Request request = new Request.Builder().url(serverUrl).post(body).build();
    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.body() == null) {
            throw new StoneCommonClientException("Call service Response body is null", 500,
                serverUrl, body);
          }
          return response.body().string();
        }
      });
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call service fail", 500, serverUrl);
    }
  }

  public static String get(OkHttpSupport client,String serverUrl) {
    Request request = new Request.Builder().url(serverUrl).get().build();
    try {
      return (String) client.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
          if (response.body() == null) {
            throw new StoneCommonClientException("Call service Response body is null", 500,
                serverUrl);
          }
          return response.body().string();
        }
      });
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call service fail", 500, serverUrl);
    }
  }

  public static <T> R<T> formJson(String json, Class<T> clazz) {
    R<T> result;
    try {
      result = JSON.parseObject(json, new TypeReference<R<T>>(clazz) {});
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call stone-auth service,return type mismatch", 500, json);
    }
    if (!result.isSuccess()) {
      throw new StoneCommonClientException(result.getMessage(), result.getCode(), json);
    }
    return result;
  }

}
