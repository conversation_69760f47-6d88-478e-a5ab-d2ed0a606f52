package com.fxiaoke.stone.commons.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneFileClient;
import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.api.adapt.StoneFileUploadApi;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.util.CallServiceUtil;
import com.github.autoconf.ConfigFactory;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import org.jetbrains.annotations.NotNull;

/**
 * Stone 文件服务客户端实现
 *
 * <h3>架构决策 (最终版):</h3>
 * <ol>
 *   <li><strong>共享DTO与工具类</strong>: 完全采用 {@code fs-stone-common-lib} 中定义的 {@link StoneFileUploadApi} 作为API契约，并使用增强后的 {@link CallServiceUtil} 处理HTTP请求，实现了代码复用和风格统一。</li>
 *   <li><strong>适配器模式</strong>: 通过私有的适配器方法，清晰地隔离了客户端接口模型与API传输模型的转换逻辑。</li>
 *   <li><strong>依赖注入与灵活配置</strong>: 保持了通过构造函数注入依赖和配置（缓存、缓冲区大小）的优秀实践。</li>
 *   <li><strong>健壮的缓存与资源管理</strong>: 维持了 {@code try-finally} 的临时文件清理机制和缓存大小超限的快速失败策略。</li>
 *   <li><strong>明确的异常处理</strong>: 所有可预见的错误都被统一包装为 {@link StoneCommonClientException} 抛出。</li>
 *   <li><strong>性能优化</strong>: 对已知大小的文件，继续使用OkHttp的流式RequestBody以优化内存使用。</li>
 * </ol>
 */
@Slf4j
public class StoneFileClientImpl implements StoneFileClient {

  private final OkHttpSupport client;

  private String stoneUploadFileRequestTemplate;

  // API 定义常量
  private static final String USER_EXT_HEADER_NAME = "x-fs-rest-user-ext";
  private static final MediaType OCTET_STREAM_MEDIA_TYPE = MediaType.parse(
      "application/octet-stream");

  // 默认配置常量
  private static final long DEFAULT_MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
  private static final int DEFAULT_BUFFER_SIZE = 8192; // 8KB

  public StoneFileClientImpl(OkHttpSupport client) {
    this.client = client;
    registerConfiguration();
  }

  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      stoneUploadFileRequestTemplate = config.get(
          "cms.stone.commons.client.stoneUploadFileRequestTemplate");
    });
  }

  @Override
  public StoneUploadFile.Result uploadNFile(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {
    if (arg.getFileSize() == null || arg.getFileSize() <= 0) {

      long cacheSize = maxCacheSize > 0 ? maxCacheSize : DEFAULT_MAX_CACHE_SIZE;
      int buffer = bufferSize > 0 ? bufferSize : DEFAULT_BUFFER_SIZE;

      return uploadWithCaching(arg, cacheSize, buffer, stream);
    } else {
      RequestBody requestBody = createStreamingRequestBody(stream, arg.getFileSize());
      return executeUpload(arg, requestBody);
    }
  }

  private StoneUploadFile.Result uploadWithCaching(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {
    Path tempFile = null;
    try {
      tempFile = Files.createTempFile("stone-upload-", ".tmp");
      long actualSize = 0;
      byte[] buffer = new byte[bufferSize];
      int bytesRead;

      try (OutputStream os = Files.newOutputStream(tempFile)) {
        while ((bytesRead = stream.read(buffer)) != -1) {
          actualSize += bytesRead;
          if (actualSize > maxCacheSize) {
            String message =
                "File size " + actualSize + " bytes exceeds max cache limit of " + maxCacheSize
                    + " bytes.";
            throw new StoneCommonClientException(message, 413, arg);
          }
          os.write(buffer, 0, bytesRead);
        }
      }

      arg.setFileSize((int) actualSize);

      RequestBody requestBody = RequestBody.create(tempFile.toFile(), OCTET_STREAM_MEDIA_TYPE);
      return executeUpload(arg, requestBody);

    } catch (IOException e) {
      throw new StoneCommonClientException(e, "Upload file fail", 500, arg);
    } finally {
      if (tempFile != null) {
        try {
          Files.deleteIfExists(tempFile);
        } catch (IOException e) {
          log.error("CRITICAL: Failed to delete temporary file: {}", tempFile.toAbsolutePath());
        }
      }
    }
  }

  private StoneUploadFile.Result executeUpload(StoneUploadFile.Arg arg, RequestBody requestBody)
      throws StoneCommonClientException {
    StoneFileUploadApi.Req apiRequest = adaptToApiRequest(arg);
    String userExtHeaderValue = JSON.toJSONString(apiRequest);

    String responseBody = post(client, stoneUploadFileRequestTemplate,
        Collections.singletonMap(USER_EXT_HEADER_NAME, userExtHeaderValue), requestBody);

    StoneFileUploadApi.Res apiResponse = JSON.parseObject(responseBody,
        StoneFileUploadApi.Res.class);
    return adaptToClientResult(apiResponse);
  }

  private StoneFileUploadApi.Req adaptToApiRequest(StoneUploadFile.Arg arg) {
    StoneFileUploadApi.Req req = new StoneFileUploadApi.Req();
    req.setEa(arg.getEa());
    req.setEmployee_id(arg.getEmployeeId());
    req.setBusiness(arg.getBusiness());
    req.setSecurity_group(arg.getSecurityGroup());
    req.setExtension_name(arg.getExtension());
    req.setExpire_day(arg.getExpireDay());
    req.setFile_size(arg.getFileSize());
    req.setCode(arg.getHashCode());
    req.setKeep_format(arg.getKeepFormat());
    req.setNeed_cdn(arg.getResourceType() != null && arg.getResourceType().name().contains("C"));
    return req;
  }

  private StoneUploadFile.Result adaptToClientResult(StoneFileUploadApi.Res res) {
    StoneUploadFile.Result result = new StoneUploadFile.Result();
    result.setPath(res.getPath());
    result.setSize(res.getSize());
    result.setExtension(res.getExtensionName());
    return result;
  }

  private RequestBody createStreamingRequestBody(final InputStream inputStream,
      final long contentLength) {
    return new RequestBody() {
      @Override
      public MediaType contentType() {
        return OCTET_STREAM_MEDIA_TYPE;
      }

      @Override
      public long contentLength() {
        return contentLength;
      }

      @Override
      public void writeTo(@NotNull BufferedSink sink) throws IOException {
        try (Source source = Okio.source(inputStream)) {
          sink.writeAll(source);
        }
      }
    };
  }

  /**
   * 支持自定义 Header 的 POST 请求
   *
   * @param client    OkHttp 客户端
   * @param serverUrl 请求地址
   * @param headers   自定义请求头 Map
   * @param body      请求体
   * @return 响应体字符串
   * @throws StoneCommonClientException 调用失败时抛出异常
   */
  public static String post(OkHttpSupport client, String serverUrl, Map<String, String> headers,
      RequestBody body) {
    Request.Builder requestBuilder = new Request.Builder().url(serverUrl);
    if (headers != null && !headers.isEmpty()) {
      for (Map.Entry<String, String> entry : headers.entrySet()) {
        requestBuilder.header(entry.getKey(), entry.getValue());
      }
    }
    Request request = requestBuilder.post(body).build();
    try {
      SyncCallback syncCallback = initSyncCallback(serverUrl, headers);
      return (String) client.syncExecute(request, syncCallback);
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      throw new StoneCommonClientException(e, "Call service fail", 500, serverUrl);
    }
  }

  private static SyncCallback initSyncCallback(String serverUrl, Object... args) {
    return new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response.body() == null) {
          throw new StoneCommonClientException("Call service Response body is null", 500, serverUrl,
              args);
        }
        if (!response.isSuccessful()) {
          throw new StoneCommonClientException(response.message(), response.code(), serverUrl,
              args);
        }
        return response.body().string();
      }
    };
  }

}
