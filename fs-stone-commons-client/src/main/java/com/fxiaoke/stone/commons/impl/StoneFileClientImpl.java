package com.fxiaoke.stone.commons.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneFileClient;
import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.api.adapt.StoneFileUploadApi;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.util.CallServiceUtil;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

/**
 * Stone 文件服务客户端实现
 *
 * <h3>架构决策 (最终版):</h3>
 * <ol>
 *   <li><strong>共享DTO与工具类</strong>: 完全采用 {@code fs-stone-common-lib} 中定义的 {@link StoneFileUploadApi} 作为API契约，并使用增强后的 {@link CallServiceUtil} 处理HTTP请求，实现了代码复用和风格统一。</li>
 *   <li><strong>适配器模式</strong>: 通过私有的适配器方法，清晰地隔离了客户端接口模型与API传输模型的转换逻辑。</li>
 *   <li><strong>依赖注入与灵活配置</strong>: 保持了通过构造函数注入依赖和配置（缓存、缓冲区大小）的优秀实践。</li>
 *   <li><strong>健壮的缓存与资源管理</strong>: 维持了 {@code try-finally} 的临时文件清理机制和缓存大小超限的快速失败策略。</li>
 *   <li><strong>明确的异常处理</strong>: 所有可预见的错误都被统一包装为 {@link StoneCommonClientException} 抛出。</li>
 *   <li><strong>性能优化</strong>: 对已知大小的文件，继续使用OkHttp的流式RequestBody以优化内存使用。</li>
 * </ol>
 */
@Slf4j
public class StoneFileClientImpl implements StoneFileClient {

  private final OkHttpSupport client;

  private String stoneUploadFileRequestTemplate;

  // API 定义常量
  private static final String USER_EXT_HEADER_NAME = "x-fs-rest-user-ext";
  private static final MediaType OCTET_STREAM_MEDIA_TYPE = MediaType.parse(
      "application/octet-stream");

  // 默认配置常量
  private static final long DEFAULT_MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
  private static final int DEFAULT_BUFFER_SIZE = 8192; // 8KB

  public StoneFileClientImpl(OkHttpSupport client) {
    this.client = client;
    registerConfiguration();
  }

  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      stoneUploadFileRequestTemplate = config.get(
          "cms.stone.commons.client.stoneUploadFileRequestTemplate");
    });
  }

  @Override
  public StoneUploadFile.Result uploadNFile(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {

    // 参数验证
    validateUploadArguments(arg, stream);

    // 记录上传开始日志
    log.info("Starting file upload - ea: {}, business: {}, extension: {}, fileSize: {}",
        arg.getEa(), arg.getBusiness(), arg.getExtension(), arg.getFileSize());

    if (arg.getFileSize() == null || arg.getFileSize() <= 0) {
      long cacheSize = maxCacheSize > 0 ? maxCacheSize : DEFAULT_MAX_CACHE_SIZE;
      int buffer = bufferSize > 0 ? bufferSize : DEFAULT_BUFFER_SIZE;
      return uploadWithCaching(arg, cacheSize, buffer, stream);
    } else {
      RequestBody requestBody = createStreamingRequestBody(stream, arg.getFileSize().longValue());
      return executeUpload(arg, requestBody);
    }
  }

  /**
   * 验证上传参数
   */
  private void validateUploadArguments(StoneUploadFile.Arg arg, InputStream stream) {
    if (arg == null) {
      throw new StoneCommonClientException("Upload argument cannot be null", 400);
    }
    if (stream == null) {
      throw new StoneCommonClientException("Input stream cannot be null", 400);
    }
    if (arg.getEa() == null || arg.getEa().trim().isEmpty()) {
      throw new StoneCommonClientException("Enterprise account (ea) cannot be empty", 400, arg);
    }
    if (arg.getBusiness() == null || arg.getBusiness().trim().isEmpty()) {
      throw new StoneCommonClientException("Business cannot be empty", 400, arg);
    }
    if (arg.getExtension() == null || arg.getExtension().trim().isEmpty()) {
      throw new StoneCommonClientException("File extension cannot be empty", 400, arg);
    }
  }

  private StoneUploadFile.Result uploadWithCaching(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {
    Path tempFile = null;
    long actualSize = 0;

    try {
      tempFile = Files.createTempFile("stone-upload-", ".tmp");
      log.debug("Created temporary file: {} for caching upload", tempFile.toAbsolutePath());

      byte[] buffer = new byte[bufferSize];
      int bytesRead;

      try (OutputStream os = Files.newOutputStream(tempFile)) {
        while ((bytesRead = stream.read(buffer)) != -1) {
          actualSize += bytesRead;
          if (actualSize > maxCacheSize) {
            String message = String.format(
                "File size %d bytes exceeds max cache limit of %d bytes",
                actualSize, maxCacheSize);
            log.warn("Upload rejected - {}, ea: {}, business: {}",
                message, arg.getEa(), arg.getBusiness());
            throw new StoneCommonClientException(message, 413, arg);
          }
          os.write(buffer, 0, bytesRead);
        }
      }

      // 类型安全检查：确保actualSize不会溢出Integer
      if (actualSize > Integer.MAX_VALUE) {
        throw new StoneCommonClientException(
            "File size too large for integer: " + actualSize, 413, arg);
      }

      arg.setFileSize((int) actualSize);
      log.debug("Cached file size: {} bytes", actualSize);

      RequestBody requestBody = RequestBody.create(tempFile.toFile(), OCTET_STREAM_MEDIA_TYPE);
      return executeUpload(arg, requestBody);

    } catch (StoneCommonClientException e) {
      // 重新抛出业务异常
      throw e;
    } catch (IOException e) {
      log.error("IO error during file caching - ea: {}, business: {}, actualSize: {}",
          arg.getEa(), arg.getBusiness(), actualSize, e);
      throw new StoneCommonClientException(e, "Failed to cache file for upload", 500, arg);
    } finally {
      // 确保临时文件被清理
      if (tempFile != null) {
        try {
          boolean deleted = Files.deleteIfExists(tempFile);
          if (deleted) {
            log.debug("Successfully deleted temporary file: {}", tempFile.toAbsolutePath());
          }
        } catch (IOException e) {
          log.error("CRITICAL: Failed to delete temporary file: {} - This may cause disk space leak",
              tempFile.toAbsolutePath(), e);
        }
      }
    }
  }

  private StoneUploadFile.Result executeUpload(StoneUploadFile.Arg arg, RequestBody requestBody)
      throws StoneCommonClientException {
    try {
      StoneFileUploadApi.Req apiRequest = adaptToApiRequest(arg);
      String userExtHeaderValue = JSON.toJSONString(apiRequest);

      log.debug("Executing upload request - URL: {}, Headers: {}",
          stoneUploadFileRequestTemplate, Collections.singletonMap(USER_EXT_HEADER_NAME, "[REDACTED]"));

      // 使用增强的HTTP请求方法，支持自定义Header
      String responseBody = post(client, stoneUploadFileRequestTemplate,
          Collections.singletonMap(USER_EXT_HEADER_NAME, userExtHeaderValue), requestBody);

      StoneFileUploadApi.Res apiResponse = JSON.parseObject(responseBody,
          StoneFileUploadApi.Res.class);

      StoneUploadFile.Result result = adaptToClientResult(apiResponse);
      log.info("Upload completed successfully - path: {}, size: {}",
          result.getPath(), result.getSize());

      return result;
    } catch (Exception e) {
      log.error("Upload execution failed - ea: {}, business: {}",
          arg.getEa(), arg.getBusiness(), e);
      if (e instanceof StoneCommonClientException) {
        throw e;
      }
      throw new StoneCommonClientException(e, "Failed to execute upload", 500, arg);
    }
  }

  private StoneFileUploadApi.Req adaptToApiRequest(StoneUploadFile.Arg arg) {
    StoneFileUploadApi.Req req = new StoneFileUploadApi.Req();
    req.setEa(arg.getEa());
    req.setEmployee_id(arg.getEmployeeId());
    req.setBusiness(arg.getBusiness());
    req.setSecurity_group(arg.getSecurityGroup());
    req.setExtension_name(arg.getExtension());
    req.setExpire_day(arg.getExpireDay());
    req.setFile_size(arg.getFileSize());
    req.setCode(arg.getHashCode());
    req.setKeep_format(arg.getKeepFormat());
    req.setNeed_cdn(arg.getResourceType() != null && arg.getResourceType().name().contains("C"));
    return req;
  }

  private StoneUploadFile.Result adaptToClientResult(StoneFileUploadApi.Res res) {
    StoneUploadFile.Result result = new StoneUploadFile.Result();
    result.setPath(res.getPath());
    result.setSize(res.getSize());
    result.setExtension(res.getExtensionName());
    return result;
  }

  /**
   * 创建流式RequestBody，优化内存使用
   *
   * @param inputStream 输入流
   * @param contentLength 内容长度
   * @return RequestBody实例
   */
  private RequestBody createStreamingRequestBody(final InputStream inputStream,
      final long contentLength) {
    return new RequestBody() {
      @Override
      public MediaType contentType() {
        return OCTET_STREAM_MEDIA_TYPE;
      }

      @Override
      public long contentLength() {
        return contentLength;
      }

      @Override
      public void writeTo(@NotNull BufferedSink sink) throws IOException {
        // 确保InputStream在使用后被正确关闭
        try (Source source = Okio.source(inputStream)) {
          long bytesWritten = sink.writeAll(source);
          log.debug("Streamed {} bytes to request body", bytesWritten);

          // 验证写入的字节数是否与预期一致
          if (bytesWritten != contentLength) {
            log.warn("Bytes written ({}) does not match expected content length ({})",
                bytesWritten, contentLength);
          }
        } catch (IOException e) {
          log.error("Error writing stream to request body", e);
          throw e;
        }
      }
    };
  }

  /**
   * 支持自定义 Header 的 POST 请求
   *
   * <p>此方法专门用于文件上传场景，支持自定义请求头。
   * 与 {@link CallServiceUtil#post} 的区别在于支持Header定制。</p>
   *
   * @param client    OkHttp 客户端
   * @param serverUrl 请求地址
   * @param headers   自定义请求头 Map
   * @param body      请求体
   * @return 响应体字符串
   * @throws StoneCommonClientException 调用失败时抛出异常
   */
  private static String post(OkHttpSupport client, String serverUrl, Map<String, String> headers,
      RequestBody body) {
    if (client == null) {
      throw new StoneCommonClientException("HTTP client cannot be null", 500, serverUrl);
    }
    if (serverUrl == null || serverUrl.trim().isEmpty()) {
      throw new StoneCommonClientException("Server URL cannot be empty", 500);
    }
    if (body == null) {
      throw new StoneCommonClientException("Request body cannot be null", 500, serverUrl);
    }

    Request.Builder requestBuilder = new Request.Builder().url(serverUrl);

    // 添加自定义请求头
    if (headers != null && !headers.isEmpty()) {
      for (Map.Entry<String, String> entry : headers.entrySet()) {
        if (entry.getKey() != null && entry.getValue() != null) {
          requestBuilder.header(entry.getKey(), entry.getValue());
        }
      }
    }
    Request request = requestBuilder.post(body).build();
    try {
      SyncCallback syncCallback = createResponseCallback(serverUrl, headers);
      return (String) client.syncExecute(request, syncCallback);
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      log.error("HTTP request failed - URL: {}, Headers: {}", serverUrl, headers, e);
      throw new StoneCommonClientException(e, "HTTP request execution failed", 500, serverUrl);
    }
  }

  /**
   * 创建响应回调处理器
   */
  private static SyncCallback createResponseCallback(String serverUrl, Object... args) {
    return new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response.body() == null) {
          throw new StoneCommonClientException("Response body is null", 500, serverUrl, args);
        }
        if (!response.isSuccessful()) {
          String errorMessage = String.format("HTTP %d: %s", response.code(), response.message());
          throw new StoneCommonClientException(errorMessage, response.code(), serverUrl, args);
        }
        return response.body().string();
      }
    };
  }

}
