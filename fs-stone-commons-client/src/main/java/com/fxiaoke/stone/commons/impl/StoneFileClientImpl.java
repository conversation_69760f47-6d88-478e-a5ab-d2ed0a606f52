package com.fxiaoke.stone.commons.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneFileClient;
import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.api.adapt.StoneFileUploadApi;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import com.fxiaoke.stone.commons.util.CallServiceUtil;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

/**
 * Stone 文件服务客户端实现
 *
 * <h3>架构决策 (修订版):</h3>
 * <ol>
 *   <li><strong>共享DTO与工具类</strong>: 完全采用 {@code fs-stone-common-lib} 中定义的 {@link StoneFileUploadApi} 作为API契约，实现了代码复用和风格统一。</li>
 *   <li><strong>适配器模式</strong>: 通过私有的适配器方法，清晰地隔离了客户端接口模型与API传输模型的转换逻辑。</li>
 *   <li><strong>依赖注入与灵活配置</strong>: 保持了通过构造函数注入依赖和配置（缓存、缓冲区大小）的优秀实践。</li>
 *   <li><strong>分层异常处理</strong>: 按照异常性质进行清晰分层：
 *     <ul>
 *       <li>参数验证异常 (400系列) - 在方法入口统一处理</li>
 *       <li>业务限制异常 (413等) - 在具体业务逻辑中抛出</li>
 *       <li>技术异常 (500系列) - 在底层技术调用中处理</li>
 *       <li>资源管理异常 - 独立处理，不影响主流程</li>
 *     </ul>
 *   </li>
 *   <li><strong>单一职责原则</strong>: 每个私有方法专注于单一类型的异常处理，避免异常杂糅。</li>
 *   <li><strong>健壮的资源管理</strong>: 临时文件清理采用独立方法，确保资源释放不受业务异常影响。</li>
 *   <li><strong>性能优化</strong>: 对已知大小的文件，继续使用OkHttp的流式RequestBody以优化内存使用。</li>
 * </ol>
 */
@Slf4j
public class StoneFileClientImpl implements StoneFileClient {

  private final OkHttpSupport client;

  private String stoneUploadFileRequestTemplate;

  // API 定义常量
  private static final String USER_EXT_HEADER_NAME = "x-fs-rest-user-ext";
  private static final MediaType OCTET_STREAM_MEDIA_TYPE = MediaType.parse(
      "application/octet-stream");

  // 默认配置常量
  private static final long DEFAULT_MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
  private static final int DEFAULT_BUFFER_SIZE = 8192; // 8KB

  public StoneFileClientImpl(OkHttpSupport client) {
    this.client = client;
    registerConfiguration();
  }

  private void registerConfiguration() {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      stoneUploadFileRequestTemplate = config.get(
          "cms.stone.commons.client.stoneUploadFileRequestTemplate");
    });
  }

  @Override
  public StoneUploadFile.Result uploadNFile(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {

    try {
      // 第一层：参数验证异常 (400系列)
      validateUploadArguments(arg, stream);

      // 记录上传开始日志
      log.info("Starting file upload - ea: {}, business: {}, extension: {}, fileSize: {}",
          arg.getEa(), arg.getBusiness(), arg.getExtension(), arg.getFileSize());

      // 第二层：业务逻辑处理
      return processUpload(arg, maxCacheSize, bufferSize, stream);

    } catch (StoneCommonClientException e) {
      // 业务异常直接重新抛出，保持原有的错误码和消息
      log.error("Upload failed - ea: {}, business: {}, error: {}",
          arg != null ? arg.getEa() : "null",
          arg != null ? arg.getBusiness() : "null",
          e.getMessage());
      throw e;
    } catch (Exception e) {
      // 第三层：未预期的系统异常统一处理 (500系列)
      log.error("Unexpected error during upload - ea: {}, business: {}",
          arg != null ? arg.getEa() : "null",
          arg != null ? arg.getBusiness() : "null", e);
      throw new StoneCommonClientException(e, "Unexpected upload error", 500, arg);
    }
  }

  /**
   * 处理上传逻辑 - 分离业务逻辑和异常处理
   */
  private StoneUploadFile.Result processUpload(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {

    if (arg.getFileSize() == null || arg.getFileSize() <= 0) {
      // 未知文件大小：使用缓存模式
      long cacheSize = maxCacheSize > 0 ? maxCacheSize : DEFAULT_MAX_CACHE_SIZE;
      int buffer = bufferSize > 0 ? bufferSize : DEFAULT_BUFFER_SIZE;
      return uploadWithCaching(arg, cacheSize, buffer, stream);
    } else {
      // 已知文件大小：使用流式模式
      RequestBody requestBody = createStreamingRequestBody(stream, arg.getFileSize().longValue());
      return executeUpload(arg, requestBody);
    }
  }

  /**
   * 验证上传参数
   */
  private void validateUploadArguments(StoneUploadFile.Arg arg, InputStream stream) {
    if (arg == null) {
      throw new StoneCommonClientException("Upload argument cannot be null", 400);
    }
    if (stream == null) {
      throw new StoneCommonClientException("Input stream cannot be null", 400);
    }
    if (arg.getEa() == null || arg.getEa().trim().isEmpty()) {
      throw new StoneCommonClientException("Enterprise account (ea) cannot be empty", 400, arg);
    }
    if (arg.getBusiness() == null || arg.getBusiness().trim().isEmpty()) {
      throw new StoneCommonClientException("Business cannot be empty", 400, arg);
    }
    if (arg.getExtension() == null || arg.getExtension().trim().isEmpty()) {
      throw new StoneCommonClientException("File extension cannot be empty", 400, arg);
    }
  }

  /**
   * 缓存上传模式 - 清晰的异常分层处理
   */
  private StoneUploadFile.Result uploadWithCaching(StoneUploadFile.Arg arg, long maxCacheSize,
      int bufferSize, InputStream stream) throws StoneCommonClientException {
    Path tempFile = null;
    long actualSize = 0;

    try {
      // 资源创建阶段
      tempFile = createTempFile();

      // 数据缓存阶段 - 可能抛出业务限制异常
      actualSize = cacheStreamToFile(stream, tempFile, maxCacheSize, bufferSize, arg);

      // 文件大小设置阶段 - 可能抛出数据类型异常
      setFileSizeAfterCaching(arg, actualSize);

      // 上传执行阶段
      RequestBody requestBody = RequestBody.create(tempFile.toFile(), OCTET_STREAM_MEDIA_TYPE);
      return executeUpload(arg, requestBody);

    } finally {
      // 资源清理阶段 - 独立处理，不影响主流程
      cleanupTempFile(tempFile);
    }
  }

  /**
   * 创建临时文件
   */
  private Path createTempFile() throws StoneCommonClientException {
    try {
      Path tempFile = Files.createTempFile("stone-upload-", ".tmp");
      log.debug("Created temporary file: {} for caching upload", tempFile.toAbsolutePath());
      return tempFile;
    } catch (IOException e) {
      throw new StoneCommonClientException(e, "Failed to create temporary file", 500);
    }
  }

  /**
   * 缓存流数据到文件 - 精确区分输入流和输出流异常
   */
  private long cacheStreamToFile(InputStream stream, Path tempFile, long maxCacheSize,
      int bufferSize, StoneUploadFile.Arg arg) throws StoneCommonClientException {

    long actualSize = 0;
    byte[] buffer = new byte[bufferSize];
    int bytesRead;

    try (OutputStream os = Files.newOutputStream(tempFile)) {
      while ((bytesRead = readFromInputStream(stream, buffer, actualSize, arg)) != -1) {
        actualSize += bytesRead;

        // 业务限制检查：文件大小超限
        if (actualSize > maxCacheSize) {
          String message = String.format(
              "File size %d bytes exceeds max cache limit of %d bytes",
              actualSize, maxCacheSize);
          log.warn("Upload rejected - {}, ea: {}, business: {}",
              message, arg.getEa(), arg.getBusiness());
          throw new StoneCommonClientException(message, 413, arg);
        }

        // 写入临时文件 - 区分输出流异常
        writeToTempFile(os, buffer, bytesRead, actualSize, arg);
      }

      log.debug("Cached file size: {} bytes", actualSize);
      return actualSize;

    } catch (StoneCommonClientException e) {
      // 业务异常直接重新抛出
      throw e;
    } catch (IOException e) {
      // 临时文件创建或关闭异常
      log.error("Temp file operation failed - ea: {}, business: {}, actualSize: {}",
          arg.getEa(), arg.getBusiness(), actualSize, e);
      throw new StoneCommonClientException(e, "Temporary file operation failed", 500, arg);
    }
  }

  /**
   * 从输入流读取数据 - 专门处理输入流断开异常
   */
  private int readFromInputStream(InputStream stream, byte[] buffer, long currentSize,
      StoneUploadFile.Arg arg) throws StoneCommonClientException {
    try {
      return stream.read(buffer);
    } catch (IOException e) {
      // 精确识别输入流断开异常
      String errorType = classifyInputStreamError(e);
      log.error("Input stream read failed - ea: {}, business: {}, currentSize: {}, errorType: {}",
          arg.getEa(), arg.getBusiness(), currentSize, errorType, e);

      throw new StoneCommonClientException(e,
          String.format("Input stream read failed (%s) after %d bytes", errorType, currentSize),
          400, arg);
    }
  }

  /**
   * 写入临时文件 - 专门处理输出流异常
   */
  private void writeToTempFile(OutputStream os, byte[] buffer, int bytesToWrite,
      long currentSize, StoneUploadFile.Arg arg) throws StoneCommonClientException {
    try {
      os.write(buffer, 0, bytesToWrite);
    } catch (IOException e) {
      // 精确识别输出流异常
      log.error("Temp file write failed - ea: {}, business: {}, currentSize: {}, bytesToWrite: {}",
          arg.getEa(), arg.getBusiness(), currentSize, bytesToWrite, e);

      throw new StoneCommonClientException(e,
          String.format("Temporary file write failed at %d bytes", currentSize),
          507, arg); // 507 Insufficient Storage
    }
  }

  /**
   * 分类输入流异常类型
   */
  private String classifyInputStreamError(IOException e) {
    String message = e.getMessage();
    String className = e.getClass().getSimpleName();

    // 网络相关异常
    if (className.contains("Socket") || (message != null && message.contains("socket"))) {
      return "NETWORK_DISCONNECTION";
    }

    // 连接重置异常
    if (message != null && message.toLowerCase().contains("connection reset")) {
      return "CONNECTION_RESET";
    }

    // 超时异常
    if (message != null && message.toLowerCase().contains("timeout")) {
      return "READ_TIMEOUT";
    }

    // 流关闭异常
    if (message != null && message.toLowerCase().contains("closed")) {
      return "STREAM_CLOSED";
    }

    // 其他IO异常
    return "IO_ERROR";
  }

  /**
   * 设置文件大小 - 专注于数据类型安全检查
   */
  private void setFileSizeAfterCaching(StoneUploadFile.Arg arg, long actualSize)
      throws StoneCommonClientException {

    // 数据类型安全检查：确保actualSize不会溢出Integer
    if (actualSize > Integer.MAX_VALUE) {
      throw new StoneCommonClientException(
          "File size too large for integer: " + actualSize, 413, arg);
    }

    arg.setFileSize((int) actualSize);
  }

  /**
   * 清理临时文件 - 独立的资源管理，不抛出业务异常
   */
  private void cleanupTempFile(Path tempFile) {
    if (tempFile != null) {
      try {
        boolean deleted = Files.deleteIfExists(tempFile);
        if (deleted) {
          log.debug("Successfully deleted temporary file: {}", tempFile.toAbsolutePath());
        }
      } catch (IOException e) {
        log.error("CRITICAL: Failed to delete temporary file: {} - This may cause disk space leak",
            tempFile.toAbsolutePath(), e);
      }
    }
  }

  /**
   * 执行上传请求 - 分离HTTP调用和数据转换的异常处理
   */
  private StoneUploadFile.Result executeUpload(StoneUploadFile.Arg arg, RequestBody requestBody)
      throws StoneCommonClientException {
    try {
      // 数据转换阶段
      StoneFileUploadApi.Req apiRequest = adaptToApiRequest(arg);
      String userExtHeaderValue = JSON.toJSONString(apiRequest);

      log.debug("Executing upload request - URL: {}, Headers: {}",
          stoneUploadFileRequestTemplate, Collections.singletonMap(USER_EXT_HEADER_NAME, "[REDACTED]"));

      // HTTP调用阶段 - 可能抛出网络异常
      String responseBody = executeHttpRequest(userExtHeaderValue, requestBody);

      // 响应解析阶段 - 可能抛出数据格式异常
      StoneUploadFile.Result result = parseUploadResponse(responseBody);

      log.info("Upload completed successfully - path: {}, size: {}",
          result.getPath(), result.getSize());

      return result;

    } catch (StoneCommonClientException e) {
      // 业务异常直接重新抛出
      throw e;
    } catch (Exception e) {
      // 未预期异常统一处理
      log.error("Upload execution failed - ea: {}, business: {}",
          arg.getEa(), arg.getBusiness(), e);
      throw new StoneCommonClientException(e, "Failed to execute upload", 500, arg);
    }
  }

  /**
   * 执行HTTP请求 - 精确区分网络异常类型
   */
  private String executeHttpRequest(String userExtHeaderValue, RequestBody requestBody)
      throws StoneCommonClientException {
    try {
      return post(client, stoneUploadFileRequestTemplate,
          Collections.singletonMap(USER_EXT_HEADER_NAME, userExtHeaderValue), requestBody);

    } catch (StoneCommonClientException e) {
      // 检查是否包含流异常信息
      if (e.getCause() instanceof IOException) {
        IOException ioException = (IOException) e.getCause();
        String errorMessage = ioException.getMessage();

        // 如果异常消息包含流错误源信息，进行更精确的分类
        if (errorMessage != null && errorMessage.contains("Stream error")) {
          log.error("Detected stream error during HTTP request: {}", errorMessage);

          if (errorMessage.contains("INPUT_")) {
            throw new StoneCommonClientException(ioException,
                "Input stream disconnected during upload", 400, stoneUploadFileRequestTemplate);
          } else if (errorMessage.contains("HTTP_")) {
            throw new StoneCommonClientException(ioException,
                "Target API connection failed during upload", 502, stoneUploadFileRequestTemplate);
          }
        }
      }

      // 其他业务异常直接重新抛出
      throw e;
    } catch (Exception e) {
      // 分析未捕获的异常
      String errorType = classifyHttpRequestError(e);
      log.error("HTTP request failed - errorType: {}, url: {}",
          errorType, stoneUploadFileRequestTemplate, e);

      throw new StoneCommonClientException(e,
          String.format("HTTP request failed (%s)", errorType),
          getErrorCodeForHttpError(errorType), stoneUploadFileRequestTemplate);
    }
  }

  /**
   * 分类HTTP请求异常
   */
  private String classifyHttpRequestError(Exception e) {
    String message = e.getMessage();
    String className = e.getClass().getSimpleName();

    // 连接异常
    if (className.contains("Connect") || (message != null && message.contains("connect"))) {
      return "CONNECTION_FAILED";
    }

    // 超时异常
    if (className.contains("Timeout") || (message != null && message.contains("timeout"))) {
      return "REQUEST_TIMEOUT";
    }

    // SSL/TLS异常
    if (className.contains("SSL") || className.contains("TLS") ||
        (message != null && (message.contains("ssl") || message.contains("tls")))) {
      return "SSL_ERROR";
    }

    // 主机解析异常
    if (className.contains("UnknownHost") || (message != null && message.contains("unknown host"))) {
      return "DNS_RESOLUTION_FAILED";
    }

    // 其他网络异常
    return "NETWORK_ERROR";
  }

  /**
   * 根据HTTP错误类型返回相应的错误码
   */
  private int getErrorCodeForHttpError(String errorType) {
    switch (errorType) {
      case "CONNECTION_FAILED":
      case "DNS_RESOLUTION_FAILED":
        return 503; // Service Unavailable
      case "REQUEST_TIMEOUT":
        return 504; // Gateway Timeout
      case "SSL_ERROR":
        return 495; // SSL Certificate Error
      default:
        return 500; // Internal Server Error
    }
  }

  /**
   * 解析上传响应 - 专注于数据格式异常
   */
  private StoneUploadFile.Result parseUploadResponse(String responseBody)
      throws StoneCommonClientException {
    try {
      StoneFileUploadApi.Res apiResponse = JSON.parseObject(responseBody, StoneFileUploadApi.Res.class);
      return adaptToClientResult(apiResponse);
    } catch (Exception e) {
      log.error("Failed to parse upload response: {}", responseBody, e);
      throw new StoneCommonClientException(e, "Invalid response format", 502, responseBody);
    }
  }

  private StoneFileUploadApi.Req adaptToApiRequest(StoneUploadFile.Arg arg) {
    StoneFileUploadApi.Req req = new StoneFileUploadApi.Req();
    req.setEa(arg.getEa());
    req.setEmployee_id(arg.getEmployeeId());
    req.setBusiness(arg.getBusiness());
    req.setSecurity_group(arg.getSecurityGroup());
    req.setExtension_name(arg.getExtension());
    req.setExpire_day(arg.getExpireDay());
    req.setFile_size(arg.getFileSize());
    req.setCode(arg.getHashCode());
    req.setKeep_format(arg.getKeepFormat());
    req.setNeed_cdn(arg.getResourceType() != null && arg.getResourceType().name().contains("C"));
    return req;
  }

  private StoneUploadFile.Result adaptToClientResult(StoneFileUploadApi.Res res) {
    StoneUploadFile.Result result = new StoneUploadFile.Result();
    result.setPath(res.getPath());
    result.setSize(res.getSize());
    result.setExtension(res.getExtensionName());
    return result;
  }

  /**
   * 创建流式RequestBody，精确区分输入流和HTTP输出异常
   *
   * @param inputStream 输入流
   * @param contentLength 内容长度
   * @return RequestBody实例
   */
  private RequestBody createStreamingRequestBody(final InputStream inputStream,
      final long contentLength) {
    return new RequestBody() {
      @Override
      public MediaType contentType() {
        return OCTET_STREAM_MEDIA_TYPE;
      }

      @Override
      public long contentLength() {
        return contentLength;
      }

      @Override
      public void writeTo(@NotNull BufferedSink sink) throws IOException {
        long bytesWritten = 0;

        try (Source source = Okio.source(inputStream)) {
          // 分块传输，便于精确定位异常
          bytesWritten = writeStreamWithErrorTracking(source, sink, contentLength);

          log.debug("Streamed {} bytes to request body", bytesWritten);

          // 验证写入的字节数是否与预期一致
          if (bytesWritten != contentLength) {
            log.warn("Bytes written ({}) does not match expected content length ({})",
                bytesWritten, contentLength);
          }

        } catch (IOException e) {
          // 根据异常类型和已写入字节数判断异常来源
          String errorSource = determineStreamErrorSource(e, bytesWritten, contentLength);
          log.error("Stream error - source: {}, bytesWritten: {}, expectedLength: {}",
              errorSource, bytesWritten, contentLength, e);

          // 重新包装异常，提供更精确的错误信息
          throw new IOException(String.format(
              "Stream error (%s) after %d/%d bytes: %s",
              errorSource, bytesWritten, contentLength, e.getMessage()), e);
        }
      }
    };
  }

  /**
   * 分块写入流数据，便于异常追踪
   */
  private long writeStreamWithErrorTracking(Source source, BufferedSink sink, long expectedLength)
      throws IOException {
    long totalBytesWritten = 0;
    long chunkSize = 8192; // 8KB chunks

    try {
      long bytesRead;
      while ((bytesRead = source.read(sink.getBuffer(), chunkSize)) != -1) {
        totalBytesWritten += bytesRead;
        sink.flush(); // 立即刷新，便于检测HTTP输出异常

        // 定期记录进度，便于异常定位
        if (totalBytesWritten % (1024 * 1024) == 0) { // 每1MB记录一次
          log.debug("Streaming progress: {}/{} bytes", totalBytesWritten, expectedLength);
        }
      }

      return totalBytesWritten;

    } catch (IOException e) {
      // 在这里可以更精确地判断是读取还是写入异常
      log.debug("Stream error at {} bytes: {}", totalBytesWritten, e.getMessage());
      throw e;
    }
  }

  /**
   * 判断流异常的来源
   */
  private String determineStreamErrorSource(IOException e, long bytesWritten, long expectedLength) {
    String message = e.getMessage();
    String className = e.getClass().getSimpleName();

    // HTTP连接相关异常 - 通常是目标API断开
    if (className.contains("Http") || className.contains("Protocol") ||
        (message != null && (message.contains("http") || message.contains("protocol")))) {
      return "HTTP_OUTPUT_DISCONNECTION";
    }

    // Socket异常 - 需要进一步判断
    if (className.contains("Socket")) {
      // 如果已写入大部分数据，更可能是HTTP输出问题
      if (bytesWritten > expectedLength * 0.8) {
        return "HTTP_SOCKET_DISCONNECTION";
      } else {
        return "INPUT_SOCKET_DISCONNECTION";
      }
    }

    // 连接重置 - 通常是网络层问题
    if (message != null && message.toLowerCase().contains("connection reset")) {
      return bytesWritten > expectedLength * 0.5 ? "HTTP_CONNECTION_RESET" : "INPUT_CONNECTION_RESET";
    }

    // 超时异常
    if (message != null && message.toLowerCase().contains("timeout")) {
      return bytesWritten == 0 ? "INPUT_READ_TIMEOUT" : "HTTP_WRITE_TIMEOUT";
    }

    // 流关闭异常
    if (message != null && message.toLowerCase().contains("closed")) {
      return bytesWritten == 0 ? "INPUT_STREAM_CLOSED" : "HTTP_STREAM_CLOSED";
    }

    // 默认根据写入进度判断
    return bytesWritten < expectedLength * 0.1 ? "INPUT_STREAM_ERROR" : "HTTP_OUTPUT_ERROR";
  }

  /**
   * 支持自定义 Header 的 POST 请求
   *
   * <p>此方法专门用于文件上传场景，支持自定义请求头。
   * 与 {@link CallServiceUtil#post} 的区别在于支持Header定制。</p>
   *
   * @param client    OkHttp 客户端
   * @param serverUrl 请求地址
   * @param headers   自定义请求头 Map
   * @param body      请求体
   * @return 响应体字符串
   * @throws StoneCommonClientException 调用失败时抛出异常
   */
  private static String post(OkHttpSupport client, String serverUrl, Map<String, String> headers,
      RequestBody body) {
    if (client == null) {
      throw new StoneCommonClientException("HTTP client cannot be null", 500, serverUrl);
    }
    if (serverUrl == null || serverUrl.trim().isEmpty()) {
      throw new StoneCommonClientException("Server URL cannot be empty", 500);
    }
    if (body == null) {
      throw new StoneCommonClientException("Request body cannot be null", 500, serverUrl);
    }

    Request.Builder requestBuilder = new Request.Builder().url(serverUrl);

    // 添加自定义请求头
    if (headers != null && !headers.isEmpty()) {
      for (Map.Entry<String, String> entry : headers.entrySet()) {
        if (entry.getKey() != null && entry.getValue() != null) {
          requestBuilder.header(entry.getKey(), entry.getValue());
        }
      }
    }
    Request request = requestBuilder.post(body).build();
    try {
      SyncCallback syncCallback = createResponseCallback(serverUrl, headers);
      return (String) client.syncExecute(request, syncCallback);
    } catch (StoneCommonClientException e) {
      throw e;
    } catch (Exception e) {
      log.error("HTTP request failed - URL: {}, Headers: {}", serverUrl, headers, e);
      throw new StoneCommonClientException(e, "HTTP request execution failed", 500, serverUrl);
    }
  }

  /**
   * 创建响应回调处理器
   */
  private static SyncCallback createResponseCallback(String serverUrl, Object... args) {
    return new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (response.body() == null) {
          throw new StoneCommonClientException("Response body is null", 500, serverUrl, args);
        }
        if (!response.isSuccessful()) {
          String errorMessage = String.format("HTTP %d: %s", response.code(), response.message());
          throw new StoneCommonClientException(errorMessage, response.code(), serverUrl, args);
        }
        return response.body().string();
      }
    };
  }

}
