package com.fxiaoke.stone.commons.help;

import com.fxiaoke.common.Guard;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;

public class SecureCryptHelp {

  private SecureCryptHelp() {
  }

  private static Guard guard;
  private static Guard signGuard;

  static {
    ConfigFactory.getInstance().getConfig("fs-stone-common", config -> {
      guard = new Guard(config.getDecrypt("cms.stone.commons.client.iam.encryKey"));
      signGuard = new Guard(config.getDecrypt("cms.stone.commons.client.iam.signEncryKey"));
    });
  }
  public static String encry(String raw) {
    return guard.encode(raw);
  }

  public static String consistentEncry(String raw) {
    return guard.encode2(raw);
  }

  public static String decry(String ciphertext) {
    return guard.decode(ciphertext);
  }

  public static boolean isEncry(String ciphertext) {
    return ciphertext.startsWith(Constants.ENCRYPTION_PREFIX) &&
        ciphertext.endsWith(Constants.ENCRYPTION_SUFFIX);
  }

  public static String generatorRaw(Object... args) {
    return Joiner.on(Constants.DELIMITER).join(args);
  }

  public static String extractCiphertext(String encCiphertext) {
    return encCiphertext.substring(
        Constants.ENCRYPTION_PREFIX.length(),
        encCiphertext.length() - Constants.ENCRYPTION_SUFFIX.length()
    );
  }

  public static String signEncry(String raw) {
    String ciphertext = signGuard.encode(raw);
    return Constants.ENCRYPTION_PREFIX + ciphertext + Constants.ENCRYPTION_SUFFIX;
  }

  public static String signDecry(String ciphertext) {
    return signGuard.decode(ciphertext);
  }
}