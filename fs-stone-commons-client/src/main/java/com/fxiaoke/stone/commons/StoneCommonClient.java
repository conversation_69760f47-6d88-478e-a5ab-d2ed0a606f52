package com.fxiaoke.stone.commons;

import com.fxiaoke.stone.commons.domain.api.GeneratorSignDocumentPreviewUrl;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignDownloadUrl;
import com.fxiaoke.stone.commons.domain.api.GeneratorSignPreviewUrl;
import com.fxiaoke.stone.commons.domain.api.GetDomain;
import com.fxiaoke.stone.commons.domain.api.PathToCdnFile;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;

public interface StoneCommonClient {

  /**
   * 生成文件签名下载URL
   * 作用:生成一个存在有效期且免身份校验的文件下载URL。
   * 使用场景:该签名下载URL可用于向外部分享文件或跨系统下载文件。
   * @param arg {@link GeneratorSignDownloadUrl.Arg}
   *            Arg.acUser 谁使用该链接下载文件
   *              Arg.acUser.tenantId 租户ID
   *              Arg.acUser.userId 用户ID
   *              Arg.acUser.upstreamOwnerId 上游负责人用户ID，默认为-9527表示缺少上游负责人
   *              Arg.acUser.outTenantId 外部租户ID
   *              Arg.acUser.outUserId 外部用户ID
   *                允许的组合:
   *                  tenantId + userId
   *                  tenantId + outTenantId + outUserId + upstreamOwnerId
   *            Arg.fileUser 该文件属于谁,使用什么样的权限来访问此文件
   *              Arg.fileUser.tenantId 租户ID
   *              Arg.fileUser.userId 用户ID (可以为空，A类型文件需传具有该文件访问权限的员工ID)
   *              Arg.fileUser.securityGroup 安全组 (可以为空，下载文件为网盘文件需传 XiaoKeNetDisk)
   *            Arg.fileInfo 文件的基本信息
   *              Arg.fileInfo.path 文件ID，长度不应超过48，目前仅支持N_、TN_、C_、TC_开头，不需要携带扩展名
   *              Arg.fileInfo.filename 文件名，长度不应超过128，超出部分会被截断。空值默认为YYYY-MM-DD-HH+扩展名
   *              Arg.fileInfo.extension 文件扩展名，不需要带点号，空值默认为bin，仅支持字母、数字
   *            Arg.expireTime 生成链接的有效期，单位秒/s (60-604800)
   *            Arg.business 业务标识，长度不超过24个字符
   *            Arg.globalAcceleration 是否开启全球加速，默认为false
   * @return 返回结果 {@link GeneratorSignDownloadUrl.Result} path 与 url 一一对应
   *            Result.path 文件ID
   *            Result.url 下载链接
   * @throws StoneCommonClientException 异常
   *           StoneCommonClientException.code 错误码
   *           StoneCommonClientException.message 错误信息
   */
  GeneratorSignDownloadUrl.Result generatorDownloadUrl(GeneratorSignDownloadUrl.Arg arg) throws StoneCommonClientException;

  /**
   * 生成文件签名预览URL
   * 作用:生成一个存在有效期且免身份校验的文件预览URL。
   * 使用场景:该签名预览URL可用于向外部分享文件或跨系统预览文件。
   * @param arg {@link GeneratorSignPreviewUrl.Arg}
   *            Arg.acUser 谁使用该链接下载文件
   *              Arg.acUser.tenantId 租户ID
   *              Arg.acUser.userId 用户ID
   *              Arg.acUser.upstreamOwnerId 上游负责人用户ID，默认为-9527表示缺少上游负责人
   *              Arg.acUser.outTenantId 外部租户ID
   *              Arg.acUser.outUserId 外部用户ID
   *                允许的组合:
   *                  tenantId + userId
   *                  tenantId + outTenantId + outUserId + upstreamOwnerId
   *            Arg.fileUser 该文件属于谁,使用什么样的权限来访问此文件
   *              Arg.fileUser.tenantId 租户ID
   *              Arg.fileUser.userId 用户ID (可以为空，A类型文件需传具有该文件访问权限的员工ID)
   *              Arg.fileUser.securityGroup 安全组 (可以为空，下载文件为网盘文件需传 XiaoKeNetDisk)
   *            Arg.fileInfo 文件的基本信息
   *              Arg.fileInfo.path 文件ID，长度不应超过48，目前仅支持N_、TN_、C_、TC_开头，不需要携带扩展名
   *              Arg.fileInfo.filename 文件名，长度不应超过128，超出部分会被截断。空值默认为YYYY-MM-DD-HH+扩展名
   *              Arg.fileInfo.extension 文件扩展名，不需要带点号
   *                支持浏览器可以直接打开的文件类型,目前仅支持以下类型,如需支持其他类型可反馈添加
   *                  图片类型：png、webp、jpeg、jpg、bmp、gif
   *                  纯文本：txt、sql、js、css、csv、json、md、xml、py、java
   *                  多媒体：mp4、mp3
   *            Arg.expireTime 生成链接的有效期，单位秒/s (60-604800)
   *            Arg.business 业务标识，长度不超过24个字符
   *            Arg.globalAcceleration 是否开启全球加速，默认为false
   * @return 返回结果 {@link GeneratorSignPreviewUrl.Result} path 与 url 一一对应
   *            Result.path 文件ID
   *            Result.url 预览链接
   * @throws StoneCommonClientException 异常
   *            StoneCommonClientException.code 错误码
   *            StoneCommonClientException.message 错误信息
   */
  GeneratorSignPreviewUrl.Result generatorPreviewUrl(GeneratorSignPreviewUrl.Arg arg) throws StoneCommonClientException;

  /**
   * 生成文档签名预览URL
   * 作用:生成一个存在有效期且免身份校验的文档预览URL。
   * 使用场景:该签名预览URL可用于向外部分享文档或跨系统预览文档。
   * @param arg 详细说明请点链接-> {@link GeneratorSignDocumentPreviewUrl.Arg}
   *            Arg.acUser 谁使用该链接预览文档
   *              Arg.acUser.tenantId 租户ID
   *              Arg.acUser.userId 用户ID
   *              Arg.acUser.upstreamOwnerId 上游负责人用户ID，默认为-9527表示缺少上游负责人
   *              Arg.acUser.outTenantId 外部租户ID
   *              Arg.acUser.outUserId 外部用户ID
   *                允许的组合:
   *                  tenantId + userId
   *                  tenantId + outTenantId + outUserId + upstreamOwnerId
   *            Arg.fileUser 该文档属于谁,使用什么样的权限来访问此文件
   *              Arg.fileUser.tenantId 租户ID (不可为空)
   *              Arg.fileUser.userId 用户ID (可以为空，A类型文件需传具有该文件访问权限的员工ID)
   *              Arg.fileUser.securityGroup 安全组 (可以为空，预览文档为网盘文件必需传 XiaoKeNetDisk)
   *            Arg.fileInfo 文档的基本信息
   *              Arg.fileInfo.path 文件ID，长度不应超过48，目前仅支持N_、TN_、C_、TC_开头，不需要携带扩展名
   *              Arg.fileInfo.filename 文件名，长度不应超过128，超出部分会被截断。空值默认为YYYY-MM-DD-HH+扩展名
   *              Arg.fileInfo.extension 文件扩展名，不需要带点号，(不可为空)
   *                支持以下文档类型:
   *                  PDF: 1.3-1.7版本
   *                  Word: doc、docx格式
   *                  Excel: xls、xlsx格式
   *                  PPT: ppt、pptx格式
   *            Arg.expireTime 生成链接的有效期，单位秒/s (60-604800)
   *            Arg.business 业务标识，长度不超过24个字符
   *            Arg.globalAcceleration 是否开启全球加速，默认为false
   * @return 返回结果,详细说明请点链接-> {@link GeneratorSignDocumentPreviewUrl.Result}
   */
  GeneratorSignDocumentPreviewUrl.Result generatorDocumentPreviewUrl(GeneratorSignDocumentPreviewUrl.Arg arg) throws StoneCommonClientException;

  PathToCdnFile.Result pathToCdnFile(PathToCdnFile.Arg arg);

  GetDomain.Result getDomain(GetDomain.Arg arg);
}
