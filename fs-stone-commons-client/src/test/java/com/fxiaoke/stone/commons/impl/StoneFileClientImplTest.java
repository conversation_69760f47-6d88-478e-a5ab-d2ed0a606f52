package com.fxiaoke.stone.commons.impl;

import static org.junit.jupiter.api.Assertions.*;

import com.fxiaoke.stone.commons.domain.api.StoneUploadFile;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * StoneFileClientImpl 单元测试
 *
 * <p>测试文件上传的各种场景，主要验证参数验证逻辑。</p>
 */
class StoneFileClientImplTest {

  private StoneFileClientImpl stoneFileClient;

  @BeforeEach
  void setUp() {
    // 创建一个简单的实例用于测试参数验证
    stoneFileClient = new StoneFileClientImpl(null);
  }

  @Test
  void testUploadNFile_WithValidArguments_ShouldFailWithoutClient() {
    // Given
    StoneUploadFile.Arg arg = createValidUploadArg();
    arg.setFileSize(1024); // 已知文件大小，使用流式上传
    InputStream stream = new ByteArrayInputStream("test content".getBytes());

    // When & Then - 由于没有HTTP客户端，应该在执行时失败
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(arg, 1024 * 1024, 8192, stream)
    );

    // 验证异常信息包含HTTP客户端为空的提示
    assertTrue(exception.getMessage().contains("HTTP client cannot be null") ||
               exception.getMessage().contains("NullPointerException"));
  }

  @Test
  void testUploadNFile_WithNullArg_ShouldThrowException() {
    // Given
    InputStream stream = new ByteArrayInputStream("test".getBytes());

    // When & Then
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(null, 1024 * 1024, 8192, stream)
    );
    
    assertEquals(400, exception.getCode());
    assertTrue(exception.getMessage().contains("Upload argument cannot be null"));
  }

  @Test
  void testUploadNFile_WithNullStream_ShouldThrowException() {
    // Given
    StoneUploadFile.Arg arg = createValidUploadArg();

    // When & Then
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(arg, 1024 * 1024, 8192, null)
    );
    
    assertEquals(400, exception.getCode());
    assertTrue(exception.getMessage().contains("Input stream cannot be null"));
  }

  @Test
  void testUploadNFile_WithEmptyEa_ShouldThrowException() {
    // Given
    StoneUploadFile.Arg arg = createValidUploadArg();
    arg.setEa(""); // 空的企业账号
    InputStream stream = new ByteArrayInputStream("test".getBytes());

    // When & Then
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(arg, 1024 * 1024, 8192, stream)
    );
    
    assertEquals(400, exception.getCode());
    assertTrue(exception.getMessage().contains("Enterprise account (ea) cannot be empty"));
  }

  @Test
  void testUploadNFile_WithEmptyBusiness_ShouldThrowException() {
    // Given
    StoneUploadFile.Arg arg = createValidUploadArg();
    arg.setBusiness(null); // 空的业务线
    InputStream stream = new ByteArrayInputStream("test".getBytes());

    // When & Then
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(arg, 1024 * 1024, 8192, stream)
    );
    
    assertEquals(400, exception.getCode());
    assertTrue(exception.getMessage().contains("Business cannot be empty"));
  }

  @Test
  void testUploadNFile_WithEmptyExtension_ShouldThrowException() {
    // Given
    StoneUploadFile.Arg arg = createValidUploadArg();
    arg.setExtension("   "); // 空白的扩展名
    InputStream stream = new ByteArrayInputStream("test".getBytes());

    // When & Then
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(arg, 1024 * 1024, 8192, stream)
    );
    
    assertEquals(400, exception.getCode());
    assertTrue(exception.getMessage().contains("File extension cannot be empty"));
  }

  @Test
  void testUploadNFile_WithCacheSizeExceeded_ShouldThrowException() {
    // Given
    StoneUploadFile.Arg arg = createValidUploadArg();
    arg.setFileSize(null); // 使用缓存模式

    // 创建一个大于缓存限制的流
    byte[] largeContent = new byte[1024]; // 1KB内容
    InputStream stream = new ByteArrayInputStream(largeContent);

    long maxCacheSize = 512; // 设置缓存限制为512字节

    // When & Then
    StoneCommonClientException exception = assertThrows(
        StoneCommonClientException.class,
        () -> stoneFileClient.uploadNFile(arg, maxCacheSize, 8192, stream)
    );

    assertEquals(413, exception.getCode());
    assertTrue(exception.getMessage().contains("exceeds max cache limit"));
  }

  /**
   * 创建有效的上传参数
   */
  private StoneUploadFile.Arg createValidUploadArg() {
    StoneUploadFile.Arg arg = new StoneUploadFile.Arg();
    arg.setEa("test-ea");
    arg.setEmployeeId(12345);
    arg.setResourceType(FileResourceEnum.N);
    arg.setBusiness("test-business");
    arg.setSecurityGroup("default");
    arg.setExtension("jpg");
    arg.setTempFile(false);
    arg.setExpireDay(7);
    arg.setHashCode("test-hash-123");
    arg.setKeepFormat(false);
    return arg;
  }
}
