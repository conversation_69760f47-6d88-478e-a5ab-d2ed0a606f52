# 流断开异常精确识别方案

## 🎯 问题描述

在文件上传过程中，需要精确区分两种流断开情况：
1. **输入流断开**：客户端提供的 `InputStream` 读取失败
2. **目标API断开**：向上传服务器写入数据时连接失败

## 🔍 解决方案架构

### 1. 缓存模式异常识别

```java
// 分离读取和写入操作，精确定位异常源
private long cacheStreamToFile(InputStream stream, Path tempFile, ...) {
    while ((bytesRead = readFromInputStream(stream, buffer, actualSize, arg)) != -1) {
        // 写入临时文件
        writeToTempFile(os, buffer, bytesRead, actualSize, arg);
    }
}

// 专门处理输入流异常
private int readFromInputStream(InputStream stream, ...) throws StoneCommonClientException {
    try {
        return stream.read(buffer);
    } catch (IOException e) {
        String errorType = classifyInputStreamError(e);
        throw new StoneCommonClientException(e, 
            String.format("Input stream read failed (%s)", errorType), 400, arg);
    }
}

// 专门处理输出流异常  
private void writeToTempFile(OutputStream os, ...) throws StoneCommonClientException {
    try {
        os.write(buffer, 0, bytesToWrite);
    } catch (IOException e) {
        throw new StoneCommonClientException(e, 
            "Temporary file write failed", 507, arg); // 507 Insufficient Storage
    }
}
```

### 2. 流式模式异常识别

```java
// 在RequestBody中分块传输，便于异常定位
@Override
public void writeTo(@NotNull BufferedSink sink) throws IOException {
    try (Source source = Okio.source(inputStream)) {
        long bytesWritten = writeStreamWithErrorTracking(source, sink, contentLength);
    } catch (IOException e) {
        String errorSource = determineStreamErrorSource(e, bytesWritten, contentLength);
        throw new IOException(String.format(
            "Stream error (%s) after %d/%d bytes", 
            errorSource, bytesWritten, contentLength), e);
    }
}

// 根据异常特征和传输进度判断错误源
private String determineStreamErrorSource(IOException e, long bytesWritten, long expectedLength) {
    // HTTP连接异常 - 目标API问题
    if (className.contains("Http") || className.contains("Protocol")) {
        return "HTTP_OUTPUT_DISCONNECTION";
    }
    
    // Socket异常 - 根据传输进度判断
    if (className.contains("Socket")) {
        return bytesWritten > expectedLength * 0.8 ? 
            "HTTP_SOCKET_DISCONNECTION" : "INPUT_SOCKET_DISCONNECTION";
    }
    
    // 连接重置 - 根据传输进度判断
    if (message.contains("connection reset")) {
        return bytesWritten > expectedLength * 0.5 ? 
            "HTTP_CONNECTION_RESET" : "INPUT_CONNECTION_RESET";
    }
}
```

### 3. HTTP请求层异常识别

```java
private String executeHttpRequest(...) throws StoneCommonClientException {
    try {
        return post(client, url, headers, requestBody);
    } catch (StoneCommonClientException e) {
        // 检查是否包含流异常信息
        if (e.getCause() instanceof IOException) {
            String errorMessage = ioException.getMessage();
            if (errorMessage.contains("INPUT_")) {
                throw new StoneCommonClientException(ioException, 
                    "Input stream disconnected during upload", 400, url);
            } else if (errorMessage.contains("HTTP_")) {
                throw new StoneCommonClientException(ioException, 
                    "Target API connection failed during upload", 502, url);
            }
        }
    }
}
```

## 📊 异常分类与错误码

| 异常类型 | 错误码 | 描述 | 典型场景 |
|---------|--------|------|----------|
| `INPUT_STREAM_ERROR` | 400 | 输入流读取失败 | 客户端网络断开、流被关闭 |
| `INPUT_SOCKET_DISCONNECTION` | 400 | 输入流Socket断开 | 客户端网络不稳定 |
| `INPUT_CONNECTION_RESET` | 400 | 输入流连接重置 | 客户端主动断开 |
| `HTTP_OUTPUT_DISCONNECTION` | 502 | 目标API连接失败 | 服务器网络问题 |
| `HTTP_SOCKET_DISCONNECTION` | 502 | 目标API Socket断开 | 服务器负载过高 |
| `HTTP_CONNECTION_RESET` | 502 | 目标API连接重置 | 服务器主动断开 |
| `TEMPORARY_FILE_WRITE_FAILED` | 507 | 临时文件写入失败 | 磁盘空间不足 |

## 🔧 判断逻辑

### 1. 异常类型判断
- **类名包含 "Http", "Protocol"** → 目标API问题
- **类名包含 "Socket"** → 根据传输进度判断
- **消息包含 "connection reset"** → 根据传输进度判断

### 2. 传输进度判断
- **bytesWritten < 10% expectedLength** → 更可能是输入流问题
- **bytesWritten > 50% expectedLength** → 更可能是目标API问题
- **bytesWritten > 80% expectedLength** → 很可能是目标API问题

### 3. 时序判断
- **异常发生在读取阶段** → 输入流问题
- **异常发生在写入阶段** → 输出流问题
- **异常发生在HTTP请求阶段** → 网络或目标API问题

## 💡 使用建议

1. **监控告警**：根据不同的错误码设置不同的告警策略
2. **重试策略**：输入流异常通常不适合重试，目标API异常可以考虑重试
3. **用户提示**：给用户提供更精确的错误提示，便于问题排查
4. **日志记录**：记录详细的异常信息和传输进度，便于运维分析

## 🚀 扩展性

该方案具有良好的扩展性：
- 可以轻松添加新的异常类型识别规则
- 支持自定义错误码和错误消息
- 可以集成到监控系统中进行实时分析
