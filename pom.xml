<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke.common</groupId>
    <artifactId>fxiaoke-parent-pom</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.fxiaoke</groupId>
  <artifactId>fs-stone-common</artifactId>
  <version>1.7.5-SNAPSHOT</version>
  <name>fs-stone-common</name>
  <description>fs-stone-common</description>

  <properties>
    <junit-jupiter-params.version>5.10.0</junit-jupiter-params.version>
    <jmh.version>1.36</jmh.version>
    <mockito-core.version>4.11.0</mockito-core.version>
  </properties>

  <modules>
    <module>fs-stone-common-lib</module>
    <module>fs-stone-commons-client</module>
  </modules>
  <packaging>pom</packaging>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>${junit-jupiter-params.version}</version>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.openjdk.jmh</groupId>
        <artifactId>jmh-core</artifactId>
        <version>${jmh.version}</version>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.openjdk.jmh</groupId>
        <artifactId>jmh-generator-annprocess</artifactId>
        <version>${jmh.version}</version>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito-core.version}</version>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

</project>
